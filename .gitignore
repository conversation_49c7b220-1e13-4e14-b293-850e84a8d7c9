# Python cache and compiled files
__pycache__/
*.pyc
*.pyo
*.pyd
*.pdb
*.egg-info/
*.egg
dist/
build/
*.whl
*.tar.gz
# Python virtual environments
.Python
pip-log.txt
pip-delete-this-directory.txt
# Python setuptools
.eggs/
*.manifest
*.spec
# Python installer logs
pip-log.txt
pip-delete-this-directory.txt
# Python unit test / coverage reports
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
# Python translations
*.mo
*.pot
# Python Django stuff
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
# Python Flask stuff
instance/
.webassets-cache
# Python Scrapy stuff
.scrapy
# Python Sphinx documentation
docs/_build/
# Python PyBuilder
.pybuilder/
# Python Jupyter Notebook
.ipynb_checkpoints
# Python IPython
profile_default/
ipython_config.py
# Python pyenv
.python-version
# Python pipenv
Pipfile.lock
# Python PEP 582
__pypackages__/
# Python Celery stuff
celerybeat-schedule
celerybeat.pid
# Python SageMath parsed files
*.sage.py
# Python Spyder project settings
.spyderproject
.spyproject
# Python Rope project settings
.ropeproject

# Application cache and temporary files
cache_states/
html_cache/
crawl/
*.html
*.htm
*.log
debug_log.log

# Rust build artifacts and cache
target/
Cargo.lock
.cargo/
*.rlib
*.rmeta
*.so
*.dll
*.dylib
*.exe
*.pdb
*.ilk
*.exp
*.lib
*.a
# Rust incremental compilation cache
incremental/
# Rust documentation
doc/
# Rust profiling data
*.profraw
*.profdata
# Rust flamegraph output
flamegraph.svg
perf.data*

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Virtual environments
venv/
.venv/
env/
.env/

# Environment and configuration files (if they contain secrets)
.env
*.env.local
*.env.production

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Data files that shouldn't be versioned
*.csv
*.xlsx
*.xls
data/
datasets/

# pytest cache
.pytest_cache/

# mypy cache
.mypy_cache/
.dmypy.json
dmypy.json