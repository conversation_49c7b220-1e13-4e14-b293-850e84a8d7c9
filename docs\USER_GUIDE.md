# Dynamic FX Clustering Application - User Guide

## Table of Contents
1. [Overview](#overview)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Getting Started](#getting-started)
5. [Dashboard Usage](#dashboard-usage)
6. [Advanced Features](#advanced-features)
7. [Troubleshooting](#troubleshooting)
8. [FAQ](#faq)

## Overview

The Dynamic FX Clustering Application is a real-time forex market analysis tool that combines:
- **Dynamic Correlation Clustering**: Groups currency pairs based on real-time correlation patterns
- **Volatility Regime Detection**: Identifies market volatility states and transitions
- **Advanced Analytics**: Provides clustering quality metrics and market insights
- **Real-time Alerts**: Monitors market events and regime changes

### Key Features
- Real-time data from MetaTrader 5
- Interactive dashboard with multiple visualization tabs
- 7-cluster correlation analysis
- 5-regime volatility classification
- Advanced analytics and performance metrics
- Export capabilities for data and charts

## Installation

### Prerequisites
- Windows 10/11 (required for MetaTrader 5)
- Python 3.9 or higher
- MetaTrader 5 terminal installed and configured
- Rust compiler (for building clustering engine)

### Step 1: Install Python Dependencies
```powershell
# Navigate to project directory
cd C:\Users\<USER>\Desktop\Clustering

# Install required packages
pip install dash plotly pandas numpy scipy scikit-learn
pip install MetaTrader5 pytz dash-bootstrap-components
pip install psutil memory-profiler pytest
```

### Step 2: Build Rust Clustering Engine
```powershell
# Navigate to Rust project
cd cluster_core

# Build the Rust library
cargo build --release

# Return to main directory
cd ..
```

### Step 3: Configure MetaTrader 5
1. Open MetaTrader 5 terminal
2. Go to Tools → Options → Expert Advisors
3. Enable "Allow automated trading"
4. Enable "Allow DLL imports"
5. Ensure your account has access to required currency pairs

### Step 4: Verify Installation
```powershell
# Run test to verify installation
python test_clustering_engine.py
```

## Configuration

### Basic Configuration
Edit `config.py` to customize settings:

```python
# Currency pairs to monitor (default: 28 major pairs)
CURRENCY_PAIRS = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD',
    'AUDUSD', 'NZDUSD', 'EURJPY', 'EURGBP', 'EURCHF',
    # ... add or remove pairs as needed
]

# Timezone settings
MARKET_TIMEZONE = 'Europe/Bucharest'  # Adjust to your timezone

# Performance settings
UPDATE_INTERVAL = 30  # seconds between updates
DATA_HISTORY_HOURS = 24  # hours of historical data
```

### Advanced Configuration
For advanced users, additional settings are available in the clustering engine:

```python
# In your startup script
engine = ClusteringEngine(
    symbols=CURRENCY_PAIRS,
    event_threshold=0.8,      # Sensitivity for event detection
    min_data_quality=0.7,     # Minimum data quality threshold
    persistence_dir="data"    # Data storage directory
)
```

## Getting Started

### Step 1: Start the Application
```powershell
# Start the dashboard
python run_clustering_app.py
```

### Step 2: Access the Dashboard
1. Open your web browser
2. Navigate to `http://localhost:8050`
3. Wait for initial data loading (may take 30-60 seconds)

### Step 3: Verify Connection
- Check the connection status indicator in the top-right corner
- Ensure "MT5 Connected" shows green status
- Verify that clustering data is updating

## Dashboard Usage

### Main Tabs

#### 1. Clustering Analysis Tab
- **Dendrogram**: Shows hierarchical clustering of currency pairs
- **Sankey Diagram**: Displays cluster transitions over time
- **Statistics Panel**: Current clustering metrics and quality scores
- **Event Log**: Real-time market events and regime changes

**How to Use:**
- Click on dendrogram branches to explore cluster details
- Hover over Sankey flows to see transition information
- Monitor statistics panel for clustering quality
- Review event log for significant market changes

#### 2. Volatility Regimes Tab
- **Calendar View**: Daily volatility regime classification
- **Transitions Chart**: Intraday regime changes
- **Regime Explanations**: Detailed descriptions of each regime

**Regime Types:**
1. **Low Volatility**: Stable market conditions
2. **Medium Volatility**: Normal market activity
3. **High Volatility**: Increased market stress
4. **Extreme Volatility**: Significant market disruption
5. **Crisis Volatility**: Market crisis conditions

#### 3. Advanced Analytics Tab
- **Clustering Quality Metrics**: Silhouette scores, stability measures
- **Market Regime Classification**: Current market state analysis
- **Real-time Alerts**: System alerts and notifications
- **Performance Trends**: Historical performance metrics
- **Portfolio Analytics**: Advanced portfolio optimization and MPT integration

### Interactive Features

#### Cluster Selection
- Click on dendrogram branches to select specific clusters
- Selected cluster details appear in the statistics panel
- Use cluster information for trading decisions

#### Time Range Selection
- Use date pickers to analyze historical periods
- Compare clustering patterns across different timeframes
- Export historical data for further analysis

#### Real-time Updates
- Dashboard updates automatically every 30 seconds
- Manual refresh available via browser refresh
- Connection status monitored continuously

## Advanced Features

### Portfolio Analytics System

The Portfolio Analytics system provides advanced portfolio optimization and Modern Portfolio Theory (MPT) integration based on real-time clustering and volatility regime data.

#### Update Frequency
- **Portfolio Recalculation**: Every 5 minutes (300 seconds) - triggered by clustering data updates
- **Data Scope**: Uses only current day's data from 00:00 (Europe/Bucharest timezone) for intraday analysis
- **Timeframe**: 1-minute (M1) granularity for optimal statistical significance and volatility estimation

#### Portfolio Templates

The system generates 5 distinct portfolio templates, each optimized for different investment strategies:

##### 1. Conservative Risk-Parity Portfolio
- **Strategy**: Risk parity with defensive focus
- **Pair Selection**: 4 pairs selected based on:
  - Lowest volatility pairs from different clusters
  - Stable correlation patterns
  - Defensive characteristics during high volatility regimes
- **Optimization**: Equal risk contribution with 20,000 iterations
- **Minimum Weight**: 0.10 per selected pair
- **Fallback**: Equal weights if optimization fails

##### 2. Balanced Cluster-Diversified Portfolio
- **Strategy**: Balanced diversification across clusters
- **Pair Selection**: 5 pairs selected based on:
  - One representative pair from each major cluster
  - Medium volatility and correlation characteristics
  - Balanced risk-return profile
- **Optimization**: Maximum Sharpe ratio with 20,000 iterations
- **Minimum Weight**: 0.10 per selected pair
- **Risk Management**: Cluster-based diversification constraints

##### 3. Aggressive Maximum-Sharpe Portfolio
- **Strategy**: Maximum return optimization
- **Pair Selection**: 3 pairs selected based on:
  - Highest Sharpe ratio potential
  - High expected returns with acceptable risk
  - Momentum and trend characteristics
- **Optimization**: Maximum Sharpe ratio with 20,000 iterations
- **Minimum Weight**: 0.10 per selected pair (allows negative weights for hedging)
- **Risk Profile**: Higher volatility, higher expected returns

##### 4. Regime-Adaptive Dynamic Portfolio
- **Strategy**: Adapts to current volatility regime
- **Pair Selection**: 4 pairs selected based on:
  - **Low Volatility Regime**: Growth-oriented pairs with momentum
  - **Medium Volatility Regime**: Balanced pairs with stable correlations
  - **High Volatility Regime**: Defensive pairs with low correlation to risk assets
  - **Extreme/Crisis Volatility**: Safe-haven pairs (USD, CHF, JPY)
- **Optimization**: Maximum Sharpe ratio with regime-specific constraints
- **Minimum Weight**: 0.10 per selected pair
- **Dynamic Rebalancing**: Triggers on regime changes

##### 5. Equal Risk Contribution (ERC) Portfolio
- **Strategy**: Equal risk contribution from each selected pair
- **Pair Selection**: 5 pairs selected based on:
  - Diverse volatility profiles for risk balancing
  - Low correlation between selected pairs
  - Stable risk characteristics
- **Optimization**: Risk parity optimization with 20,000 iterations
- **Minimum Weight**: 0.10 per selected pair
- **Risk Budgeting**: Each pair contributes equally to total portfolio risk

#### Sharpe Ratio Calculation

**Formula**: `Sharpe Ratio = (Portfolio Return - Risk Free Rate) / Portfolio Volatility`

**Parameters**:
- **Risk-Free Rate**: 2% annually (0.02)
- **Portfolio Return**: Calculated from 1-minute log returns, annualized using: `minute_return × 1440 × 252`
- **Portfolio Volatility**: Square root of portfolio variance from covariance matrix
- **Return Caps**: Maximum 30% annually for FX markets, minimum 1% annually

**Important Notes**:
- **Negative Sharpe ratios are mathematically correct** and can occur during:
  - **Intraday drawdowns**: When current day's returns are below the 2% risk-free rate
  - **High volatility periods**: Where portfolio risk exceeds return potential
  - **Regime transitions**: During market stress when correlations break down
  - **Morning sessions**: Early trading hours with limited price movement
- **Why this happens with intraday data**:
  - System uses only current day's data from 00:00 (Europe/Bucharest timezone)
  - Short timeframes can show temporary negative performance
  - FX markets often have intraday volatility that exceeds returns
- **Optimization still works correctly**:
  - The algorithm maximizes Sharpe ratio within the given constraints
  - Negative Sharpe means "best possible" under current market conditions
  - Portfolios will show positive Sharpe ratios when market conditions improve
- **Performance normalization**: Values typically normalize over longer periods (weekly/monthly)

#### Iteration Settings

**Per Portfolio Template**: 20,000 iterations each
- **Total System Load**: 5 templates × 20,000 iterations = 100,000 total iterations per update
- **Performance Impact**: Moderate - optimizations run every 5 minutes
- **Convergence**: High iteration count ensures optimal solutions and avoids fallback to equal weights
- **Tolerance**: 1e-9 for high precision optimization

#### Weight Normalization

- **Constraint**: Sum of absolute weights = 1.0 for selected pairs only
- **Non-selected pairs**: Exactly 0.0 weight
- **Minimum weight**: 0.10 for any non-zero position
- **Short positions**: Allowed in Aggressive portfolio (negative weights for hedging)

#### MPT Integration

- **Real-time Lines**: Portfolio allocations displayed as MPT lines in the dashboard
- **Live Updates**: Synchronized with clustering data updates (every 5 minutes)
- **All Weights Display**: Shows all non-zero weights for each portfolio (not just top 3)
- **Performance Tracking**: Real-time return and risk calculations

### Export Functionality
Access export features in the Advanced Analytics tab:

1. **Data Export**:
   - CSV format for clustering results
   - JSON format for complete state data
   - Excel format for comprehensive reports

2. **Chart Export**:
   - PNG images for presentations
   - HTML files for interactive sharing
   - PDF reports for documentation

### Alert System
Configure alerts for:
- Significant clustering changes
- Volatility regime transitions
- Data quality issues
- System performance problems

### Performance Monitoring
Monitor system performance:
- Clustering computation time
- Data fetch latency
- Memory usage
- CPU utilization

## Troubleshooting

### Common Issues

#### 1. MetaTrader 5 Connection Failed
**Symptoms**: "MT5 Disconnected" status, no data updates
**Solutions**:
- Ensure MetaTrader 5 is running and logged in
- Check Expert Advisors settings (allow automated trading)
- Verify account has access to required currency pairs
- Restart MetaTrader 5 and the application

#### 2. Empty Charts or No Data
**Symptoms**: Blank charts, "No data available" messages
**Solutions**:
- Wait for initial data collection (1-2 minutes)
- Check internet connection
- Verify currency pairs are available in your MT5 account
- Check weekend/market hours (limited data during market close)

#### 3. Slow Performance
**Symptoms**: Delayed updates, high CPU usage
**Solutions**:
- Reduce number of monitored currency pairs
- Increase update interval in configuration
- Close other resource-intensive applications
- Check system requirements

#### 4. Clustering Errors
**Symptoms**: Error messages in event log, failed clustering
**Solutions**:
- Check data quality (minimum 100 data points required)
- Verify Rust clustering engine is properly built
- Review error logs for specific issues
- Restart the application

### Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| E001 | MT5 Connection Failed | Check MetaTrader 5 setup |
| E002 | Insufficient Data | Wait for more data collection |
| E003 | Clustering Failed | Check Rust engine build |
| E004 | Data Quality Low | Verify data sources |
| E005 | Memory Error | Reduce dataset size |

### Log Files
Check log files for detailed error information:
- `alerts.log`: Alert system logs
- Application console: Real-time debug information

## FAQ

### General Questions

**Q: How often does the data update?**
A:
- **Clustering data**: Every 30 seconds (configurable in `config.py`)
- **Portfolio analytics**: Every 5 minutes (300 seconds) - triggered by clustering updates
- **MPT lines**: Updated with portfolio analytics every 5 minutes
- **Chart displays**: Every 1 minute for visual updates

**Q: Can I use this with other trading platforms?**
A: Currently only MetaTrader 5 is supported. Other platforms may be added in future versions.

**Q: How much historical data is needed?**
A: Minimum 24 hours for reliable clustering. More data improves accuracy.

### Technical Questions

**Q: Why do I see different cluster counts?**
A: The algorithm automatically determines optimal cluster count (typically 3-7 clusters) based on data patterns.

**Q: What do the volatility regimes mean?**
A: Regimes represent different market volatility states, from calm (1) to crisis (5) conditions.

**Q: Can I export the clustering results?**
A: Yes, use the export functionality in the Advanced Analytics tab.

**Q: Why do I see negative Sharpe ratios in portfolio analytics?**
A: Negative Sharpe ratios are normal for intraday analysis and indicate:
- Current day's returns are below the 2% risk-free rate
- High intraday volatility relative to returns
- Market stress or regime transition periods
- The optimization is still working correctly - it's finding the "best possible" portfolio under current conditions

### Performance Questions

**Q: How much memory does the application use?**
A: Typically 200-500MB depending on data size and number of currency pairs.

**Q: Can I run this on multiple timeframes?**
A: Currently optimized for 5-minute data. Other timeframes require configuration changes.

**Q: Is real-time analysis resource intensive?**
A: Moderate resource usage. Performance depends on number of monitored pairs and update frequency.

**Q: How many optimization iterations does the portfolio system use?**
A:
- **20,000 iterations per portfolio template** (not total)
- **5 portfolios × 20,000 = 100,000 total iterations** per 5-minute update cycle
- **Performance impact**: Moderate - optimizations run only every 5 minutes
- **High iteration count ensures**: Optimal solutions and avoids fallback to equal weights

### Support

For additional support:
1. Check the troubleshooting section above
2. Review error logs for specific issues
3. Consult the developer documentation
4. Contact technical support with log files and error descriptions

## Quick Start Checklist

### For New Users
- [ ] Install Python 3.9+ and required packages
- [ ] Build Rust clustering engine
- [ ] Configure MetaTrader 5 with automated trading enabled
- [ ] Run `python run_clustering_app.py`
- [ ] Open browser to `http://localhost:8050`
- [ ] Verify MT5 connection status
- [ ] Wait for initial data collection (1-2 minutes)
- [ ] Explore clustering analysis tab
- [ ] Check volatility regimes tab
- [ ] Review advanced analytics features

### Daily Usage
- [ ] Start MetaTrader 5 and log in
- [ ] Launch the clustering application
- [ ] Monitor connection status
- [ ] Review overnight regime changes
- [ ] Check for significant clustering events
- [ ] Export data if needed for analysis

---

*Last updated: July 2025*
*Version: 1.0.0*
